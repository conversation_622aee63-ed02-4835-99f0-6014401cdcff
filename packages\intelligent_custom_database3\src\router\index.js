import { createRouter, createWebHashHistory } from 'vue-router'
import { LoadingBar } from 'view-ui-plus'
import { useLoginStore } from '@/stores/login'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/search/cd3',
      name: 'CD3',
      hidden: true,
      component: () => import('../views/SearchView.vue'),
      meta: { title: '快速查询', icon: 'md-search' }
    }
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

router.beforeEach((to, from, next) => {
  LoadingBar.start()
  document.title = (to.meta && to.meta.title) || ''
  next()
  useLoginStore().setRouterPath(to.path)
})

router.afterEach(() => {
  LoadingBar.finish()
})
export default router
