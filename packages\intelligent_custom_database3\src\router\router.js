import Layout from '@/layout/LayoutView.vue'
const layoutMap = [
  {
    path: '/search',
    name: 'search',
		hidden: true,
    component: () => import('../views/SearchView.vue'),
    meta: { title: '快速查询', icon: 'md-search' }
  }
]
const routers = [
  {
    path: '/',
    meta: { title: '快速查询' },
    redirect: { name: 'search' },
    component: Layout,
    children: [...layoutMap]
  },
  // 404页面
  { path: '/:pathMatch(.*)*', redirect: '/404' }
]
export { routers, layoutMap }
