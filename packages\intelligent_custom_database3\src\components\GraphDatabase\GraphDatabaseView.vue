<template>
  <div>
    <!-- 选择图数据库 -->
    <Modal
      v-model="graphDatabaseModal"
      title="选择图数据库"
      width="70%"
      :styles="{ top: '50px' }"
      footer-hide
      :mask-closable="false"
    >
      <div style="position: relative">
        <Spin size="large" fix :show="analysisStore.logicFlowLoading"></Spin>
        <div id="picture_database"></div>
      </div>
    </Modal>
  </div>
</template>

<script setup>
import LogicFlow from '@logicflow/core'
import '@logicflow/core/dist/style/index.css'
import { Control } from '@logicflow/extension'
import '@logicflow/extension/lib/style/index.css'
import { ref, watch } from 'vue'
import { useAnalysisStore } from '@/stores/analysis'
import { Message } from 'view-ui-plus'

import registerNode from './index'

const analysisStore = useAnalysisStore()
const graphDatabaseModal = ref(false)
const showGraphDatabase = () => {
  graphDatabaseModal.value = true
}
watch(
  () => analysisStore.logicFlowLoading,
  (newVal) => {
    if (!newVal) {
      applyGrapDatabase()
    }
  }
)
const lfInit = ref(null)
const applyGrapDatabase = () => {
  const lf = new LogicFlow({
    container: document.querySelector('#picture_database'), //获取容器
    edgeType: 'bezier',
    textEdit: false,
    background: {
      color: '#EDEDED',
      backgroundColor: '#EDEDED'
    },
    hideAnchors: true,
    plugins: [Control]
  })
  lfInit.value = lf
  registerNode(lf)
  lf.render(analysisStore.logicFlowData)
  logicFlowEvent()
}
const emit = defineEmits(['setDatabaseRel'])
// logicFlow的事件监听
const logicFlowEvent = () => {
  // 双击节点事件
  lfInit.value.on('node:dbclick', ({ data }) => {
    graphDatabaseModal.value = false
    emit('setDatabaseRel', data.properties.relStr)
    Message.success({
      background: true,
      content: '已选择图数据库!'
    })
  })
}

defineExpose({
  showGraphDatabase
})
</script>

<style lang="scss" scoped>
#picture_database {
  height: 750px;
}
</style>
