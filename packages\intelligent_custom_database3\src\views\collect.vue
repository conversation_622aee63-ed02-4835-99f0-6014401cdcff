<template>
  <div class="cellect_layout">
    <Spin size="large" fix :show="collectStore.collectDatasLoad">数据加载中……</Spin>
    <div class="cellect_left">
      <cellectTree :listType="'username'" :tableName="'favorites_data'" @getCollect="getCollect" />
    </div>
    <div class="cellect_right">
      <div class="cellect_right_tit">
        <div class="cellect_right_titl">
          <b style="font-size: 18px;">数据详情</b
          ><Button
            v-if="collectStore.collectDatasShow.length"
            style="margin-left: 10px"
            type="success"
            @click="exportToExcel"
            size="small"
            >导出</Button
          >
        </div>

        <div class="print_box">
          <Icon
            type="ios-print-outline"
            class="print_icon"
            size="26"
            @click="printCurrentPage"
          ></Icon>
        </div>
      </div>
      <div class="cellect_data">
        <div class="data_list" v-if="collectStore.collectDatasShow.length">
          <div class="twitterlistRow" v-for="(item, index) in collectStore.collectDatasShow">
            <div class="twitterlistRowMid">
              <div
                v-for="(value, key) in item.columnValues.d.file_data"
                :key="key"
                class="twitterlistRowMidcon"
              >
                <div class="twitterlistRowMidconTit">
                  <b>{{ key }}</b>
                </div>
                
                <Tooltip 
                  placement="top"
                  theme="light"
                  max-width="200"
                >
                  <template #content>
                    <div 
                      style="
                        display:flex;
                        flex-direction: row;
                        align-items: center;
                      "
                    >
                      <span>{{value.toString()}}</span>
                      <Button 
                        size="small" 
                        icon="ios-copy-outline" 
                        type="success"
                        style="margin-left:20px;min-width: 20px;max-width: 20px;"
                        title="点击复制"
                        @click="copyOne(value.toString())"
                      ></Button>
                    </div>
                  </template>
                  <div class="twitterlistRowMidconVal">
                    {{ value.toString() }}
                  </div>
                </Tooltip>
              </div>
            </div>
            <div class="twitterlistRowMidBtn">
              <Button 
                size="small" 
                style="margin-top: 10px" 
                @click="toCopy(item)" 
                type="success"
              >
                复制
              </Button>
              <Button 
                size="small" 
                style="margin-top: 10px" 
                @click="delDataFn(item)" 
                type="error"
              >
                删除
              </Button>
            </div>
          </div>

          <p
            class="search_over_tip"
            v-if="
              collectStore.dataSearchOver &&
              Math.ceil(collectStore.collectDatas.length / 20) === pageNow
            "
          >
            数据已经到底了!
          </p>
        </div>
        <div class="no_data" v-else>
          <p>未查询到数据。</p>
        </div>
      </div>
      <div style="text-align: center; margin-top: 20px;">
        <Page
          v-if="collectStore.collectDatas.length > 20"
          :total="collectStore.collectDatas.length"
          size="small"
          :page-size="20"
          @on-change="pageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElDialog, ElMessageBox, ElMessage } from 'element-plus'
import { useClipboard } from '@vueuse/core'
const { text, copy: copyToClipboard } = useClipboard()
import cellectTree from '@/components/collectTree.vue'
import { useCollectStore } from '@/stores/collect'
import { storeToRefs } from 'pinia'
import { ref, onMounted, nextTick } from 'vue'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
const pageNow = ref(1)
const collectName = ref('')
const collectStore = useCollectStore()
const { collectDatas, collectDatasShow } = storeToRefs(collectStore)

//挂载完毕onMounted
onMounted(() => {
  collectStore.setInt()
})
//方法
//打印
const printCurrentPage = () => {
  window.print()
}
const getCollect = (data) => {
  console.log('collect', data)
  collectName.value = data.title
  try {
    // 你的逻辑代码
    pageNow.value = 1
    collectStore.setInt()
    collectStore.setNowRelation(data)
    collectStore.getCollectData(pageNow.value)
  } catch (error) {
    console.error('Error in getCollect handler:', error)
    // 可以在这里添加用户友好的错误提示
  }
}
//导出
//导出
//导出
const exportToExcel = () => {
  const excelList = collectStore.collectDatas
  if (!excelList || excelList.length === 0) {
    ElMessage.warning('请选择要导出的数据')
    return
  }

  const worksheetData = []

  excelList.forEach((item) => {
    const keys = Object.keys(item.columnValues.d.file_data)
    const values = Object.values(item.columnValues.d.file_data).map((val) =>
      val !== null && val !== undefined ? String(val) : ''
    )

    worksheetData.push(keys)
    worksheetData.push(values)
    worksheetData.push([]) // 添加一个空行用于间隔
  })

  const ws = XLSX.utils.aoa_to_sheet(worksheetData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

  const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
  function formatDateTime(input, separator = '-') {
    const date = input instanceof Date ? input : new Date(input)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return [year, month, day].join(separator) + ` ${hours}:${minutes}`
  }
  let fileName = (collectName.value || 'export') + '(' + formatDateTime(new Date()) + ')' + '.xlsx'
  saveAs(new Blob([wbout], { type: 'application/octet-stream' }), fileName)
}
/* const exportToExcel = () => {
  const excelList = collectStore.collectDatas
  if (!excelList || excelList.length === 0) {
    ElMessage.warning('请选择要导出的数据')
    return
  }

  const dataForSheet = {}
  let maxRow = 0

  // 创建表头行
  const headerRow = ['']
  excelList.forEach((_, colIndex) => {
    headerRow.push(`数据${colIndex + 1}`)
  })
  dataForSheet[0] = headerRow
  maxRow = 1

  // 填充数据行
  excelList.forEach((item, colIndex) => {
    let rowIndex = 1
    for (const key in item.columnValues.d.file_data) {
      if (Object.prototype.hasOwnProperty.call(item.columnValues.d.file_data, key)) {
        // 键的行
        if (!dataForSheet[rowIndex]) dataForSheet[rowIndex] = []
        dataForSheet[rowIndex][colIndex + 1] = key
        rowIndex++

        // 值的行
        if (!dataForSheet[rowIndex]) dataForSheet[rowIndex] = []
        dataForSheet[rowIndex][colIndex + 1] = item.columnValues.d.file_data[key]
        rowIndex++
        //空一行
        if (!dataForSheet[rowIndex]) dataForSheet[rowIndex] = []
        dataForSheet[rowIndex][colIndex + 1] = ''
        rowIndex++
      }
    }
    if (rowIndex > maxRow) {
      maxRow = rowIndex
    }
  })

  // 将数据对象转换为工作表所需的数组格式 (aoa)
  const excelDataArr = []
  const numCols = excelList.length + 1
  for (let i = 0; i < maxRow; i++) {
    const sourceRow = dataForSheet[i] || []
    const targetRow = []
    for (let j = 0; j < numCols; j++) {
      const cellValue = sourceRow[j]
      targetRow.push(cellValue === undefined || cellValue === null ? '' : cellValue)
    }

    excelDataArr.push(targetRow)
  }

  // 将数据转换为工作表
  const worksheet = XLSX.utils.aoa_to_sheet(excelDataArr)

  // 设置列宽
  worksheet['!cols'] = headerRow.map(() => ({ wpx: 150 }))

  // 创建工作簿并添加工作表
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

  // 生成Excel文件
  const excelBuffer = XLSX.write(workbook, {
    bookType: 'xlsx',
    type: 'array'
  })

  // 使用blob和FileReader创建一个Blob URL
  const dataBlob = new Blob([excelBuffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
  })
  const blobUrl = window.URL.createObjectURL(dataBlob)
  function formatDateTime(input, separator = '-') {
    const date = input instanceof Date ? input : new Date(input)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return [year, month, day].join(separator) + ` ${hours}:${minutes}`
  }
  let fileName = (collectName.value || 'export') + '(' + formatDateTime(new Date()) + ')' + '.xlsx'
  // 使用saveAs下载文件
  saveAs(dataBlob, fileName)

  // 清理
  window.URL.revokeObjectURL(blobUrl)
} */
//删除
const delDataFn = (v) => {
  ElMessageBox.confirm('确定要删除?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      try {
        const result = collectStore.delDataFn(v)
        pageNow.value = 1
      } catch (error) {
        console.error('Error in delDataFn call:', error)
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消'
      })
    })
}
//点击页码
const pageChange = (v) => {
  pageNow.value = v
  console.log(v, collectDatas.value)
  if (v < 5) {
    collectDatasShow.value = collectStore.collectDatas.slice((v - 1) * 20, v * 20)
  } else {
    if (collectStore.collectDatas.length / 20 == v) {
      collectDatasShow.value = collectStore.collectDatas.slice((v - 1) * 20, v * 20)
      collectStore.getCollectData(v)
    } else {
      collectDatasShow.value = collectStore.collectDatas.slice((v - 1) * 20, v * 20)
    }
  }
  nextTick(() => {
    const dataListEl = document.querySelector('.data_list')
    if (dataListEl) {
      dataListEl.scrollTop = 0
    }
  })
}
// 单字段复制
const copyOne = (v) => {
  copyToClipboard(v)
  ElMessage({
    type: 'success',
    message: '复制成功'
  })
}
//复制
const toCopy = (v) => {
  copyToClipboard(JSON.stringify(v, null, 2))
  ElMessage({
    type: 'success',
    message: '复制成功'
  })
}
</script>

<style scoped lang="scss">
.cellect_layout {
  width: 100%;
  height: 100%;

  background-color: #fff;
  .cellect_left {
    float: left;
    width: 300px;
    height: 100%;
    // background-color: #eff1f1;
    border-right: 1px solid #e8eaec;
    overflow: auto;
  }
  .cellect_right {
    float: left;
    width: calc(100% - 300px);
    height: 100%;

    .cellect_right_tit {
      padding: 10px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #eee;
    }
    .cellect_data {
      height: 80vh;
      overflow-y: auto;
    }
  }
  .data_list {
    height: 100%;
    overflow: auto;
    .data_item {
      width: 100%;
    }
    .item {
      overflow: auto;
      width: 33%;
      margin: 0 4px 4px 0;
      border: 1px solid #e9e7e7;
      background-color: #fff;
      border-radius: 5px;
      padding: 10px;
      box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.1);
    }
    .twitterlistRow {
      display: flex;
      border-radius: 10px; /* 圆角大小 */
      box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.1);
      padding-bottom: 10px;
      padding-top: 10px;
      margin: 10px;
      .twitterlistRowLeft {
        text-align: center;
        width: 100px;
      }
      .twitterlistRowMid {
        display: flex;
        flex-wrap: wrap;
        width:95%;
        .twitterlistRowMidcon {
          padding: 10px;
          width: 14%;
          .twitterlistRowMidconTit {
            width: 150px;
            text-align: center;
          }
          .twitterlistRowMidconVal {
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 150px; /* 或固定宽度 */
            display: inline-block;
            color: #13CE66;
          }
        }
      }
      .twitterlistRowMidBtn {
        width: 150px;
        flex-wrap: wrap;
        margin-right: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap:10px;
      }
    }
  }
  .no_data {
    width: 100%;
    height: 95%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .search_over_tip {
    color: #2b85e4;
    text-align: center;
    background-color: #fff;
    padding: 10px 0;
  }
}
</style>
