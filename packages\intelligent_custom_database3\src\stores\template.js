import { defineStore } from 'pinia'
import { useLoginStore } from './login'
import { Message } from 'view-ui-plus'

export const useTemplateStore = defineStore('templateStore', {
  state: () => ({
    nowCheckType: '',
    templateList: [],
    showLoading: true,
    iconList: [
      '8e1a4496b137f1f318e83ff8291e3ff5b9d7799dc3cd954b09921d03eb929ae776d85b4b060206f482f6c29b9da9ccbbae5f43fb8a5f5465809c6558254b430a',
      '5027bb918e3a8f9a1cdf51e3cc25733bb16e115ea3aaf0646e3b31576e6b9c4ac05c745b99828ebe81336ed44e2136c5a493af08ef0e36a823cbf58dd8f504ff',
      'dfd9ffb510a8092917170cc8dc9725e181769fae892b2bf7dbf4169a4d068abcccaa9ab03d4c2e4d0e07856b12d4708aa261143e9c1ea681e4f328d136715b36',
      '9567efaf831765311671faf7b310bfe7d807a0e9ffecdc4cf73e363cb17231fee9bb2892acfb968102d4029289372d2020184d73de5eb09713af69ee2ed1b472',
      'bc75004d516c24af782adf94b4c0cd896874cd148f4f58956c1b07d8d5ef606c617e91dc390234c0c4c519d502cc3e020e533258ee14d7813978e80345e9120b',
      '97f10ff0207ad8ada6286a0a7622b43b8341c759de6e5423ce971a9e56e1c1da344ed86ba57acf5eb8e8edef9d0e69149b92183fe0e2034d6372c31b6be074c7'
    ]
  }),
  actions: {
    // 存储当前查看模式
    setTemplateType(type) {
      this.showLoading = true
      this.nowCheckType = type
    },
    // 获取模板列表
    getTemplateList() {
      this.templateList = []
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.Search.SearchList.Query',
        [
          {
            head: {
              size: 200
            },
            control: {
              query_string: '',
              query_type: 'username',
              condition: {
                query_mode: 'match'
              }
            },
            msg: {
              data_range_index_name: this.nowCheckType
            }
          }
        ],
        (res) => {
          this.templateList = res.hits.hits
          this.showLoading = false
        }
      )
    },
    // 向解析模板表中添加数据
    sendAddAnalysisOss(data) {
      this.showLoading = true
      const { $data } = useLoginStore().wsMethod
      const { msg, id } = data
      $data.sendData(
        'Api.Search.SearchList.AddOss',
        [
          {
            head: {},
            control: {
              query_type: 'username',
              index: this.nowCheckType,
              type: '_doc',
              id: id
            },
            msg: msg
          }
        ],
        (res) => {
          if (res?.status === 'ok') {
            Message.success({
              background: true,
              content: '添加成功'
            })
          } else {
            Message.error({
              background: true,
              content: '添加失败'
            })
          }
          setTimeout(() => {
            this.getTemplateList()
          }, 1000)
        }
      )
    },
    // 修改解析模板表中的数据
    updataAnalysis(data) {
      this.showLoading = true
      const { $data } = useLoginStore().wsMethod
      const { msg, id } = data
      $data.sendData(
        'Api.Search.SearchList.UpdateOss',
        [
          {
            head: {},
            control: {
              query_type: 'username',
              index: this.nowCheckType,
              type: '_doc',
              id: id
            },
            msg: msg
          }
        ],
        (res) => {
          if (res?.status === 'ok') {
            Message.success({
              background: true,
              content: '修改成功'
            })
          } else {
            Message.error({
              background: true,
              content: '修改失败'
            })
          }
          setTimeout(() => {
            this.getTemplateList()
          }, 1000)
        }
      )
    },
    // 删除解析模板表中的数据
    delAnalysisOss(id) {
      this.showLoading = true
      const { $data } = useLoginStore().wsMethod
      $data.sendData(
        'Api.Search.SearchList.DelOss',
        [
          {
            head: {},
            control: {
              query_type: 'username',
              index: this.nowCheckType,
              type: '_doc',
              id: id
            }
          }
        ],
        (res) => {
          if (res?.status === 'ok') {
            Message.success({
              background: true,
              content: '删除成功'
            })
          } else {
            Message.error({
              background: true,
              content: '删除失败'
            })
          }
          setTimeout(() => {
            this.getTemplateList()
          }, 1000)
        }
      )
    }
  }
})
