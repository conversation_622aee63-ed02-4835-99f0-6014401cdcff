import { defineStore } from 'pinia'
import { useLoginStore } from './login'
import { Message } from 'view-ui-plus'
export const useCollectStore = defineStore('collect', {
  state() {
    return {
      from: 20,
      size: 100,
      lastRow: [],
      collectDatas: [],
      collectDatasShow: [],
      nowRelation: '',
      dataSearchOver: false,
      collectDatasLoad: false
    }
  },
  actions: {
    //初始化
    setInt() {
      this.dataSearchOver = false
      this.collectDatas = []
      this.collectDatasShow = []
      this.lastRow = []
      this.nowRelation = ''
    },
    //设置当前查询数据relation
    setNowRelation(v) {
      this.nowRelation = v.id + ';' + 'searchCollect'
    },
    //删除收藏
    delDataFn(v) {
      const { $data } = useLoginStore().wsMethod
      console.log('删除', this.collectDatasShow, v)
      this.collectDatasShow = this.collectDatasShow.filter((item) => item.row !== v.row)
      $data.sendData(
        'Api.Search.SearchPrefixTable.DelData',
        [
          {
            head: { row_key: [v.row] },
            msg: {
              table: 'favorites_data',
              type: 'username',
              relation: this.nowRelation
            }
          }
        ],
        (res) => {
          if (res.status === 'ok') {
            Message.success({
              background: true,
              content: '删除成功'
            })
            /* this.dataSearchOver = false
            this.collectDatas = []
            this.collectDatasShow = []
            this.lastRow = []
            this.getCollectData(1) */
          }
        }
      )
    },
    //获取收藏数据
    getCollectData(v) {
      console.log('getCollectData:v:', v)
      const { $data } = useLoginStore().wsMethod

      /*     $data.sendData(
        'Api.Search.SearchPrefixTable.Count',
        [
          {
            head: {},
            msg: {
              table: 'favorites_data',
              prefix: '',
              type: 'username',
              relation: this.nowRelation
            }
          }
        ],
        (res) => {
          console.log('123count', res)
        }
      ) */
      this.collectDatasLoad = true
      $data.sendData(
        'Api.Search.SearchPrefixTable.Query',
        [
          {
            head: {
              row_key: this.lastRow,
              // row_key: [...new Set(this.lastRow)],
              size: this.size
            },
            msg: {
              table: 'favorites_data',
              prefix: '',
              type: 'username',
              relation: this.nowRelation
            }
          }
        ],
        (res) => {
          this.collectDatasLoad = false
          console.log('123', res)
          if (res.length > 0) {
            this.lastRow = [res[res.length - 1].row]
            this.collectDatas = this.collectDatas.concat(res)

            if (this.collectDatas.length >= 20) {
              if (v == 1) {
                this.collectDatasShow = this.collectDatas.slice(0, 20)
              }
            } else {
              if (v == 1) {
                this.collectDatasShow = this.collectDatas
                this.dataSearchOver = true
              }
            }
            if (res.length < 100) {
              Message.success({
                background: true,
                content: '已搜索路径下全部数据！'
              })
              this.dataSearchOver = true
            }
          }
        }
      )
    }
  },

  //getters有点像计算属性  添加计算后的数据
  getters: {
    //第一种写法
    /* bigSum(state) {
      return state.sum * 10 //或者用this this.sum*10
    } */
    //第二种写法
    // bigSum:state=>state.sum*10//箭头函数不能用this 只能用传参的state
  }
})
