<template>
  <div class="database_node">
    <img
      v-if="properties.icon"
      :src="'/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/' + properties.icon"
    />
    <img v-else src="@/assets/images/datatype.jpg" />
    <div v-if="properties.initialPoint">
      <Input
        v-model="databaseInputValue"
        class="database_input"
        type="textarea"
        :rows="2"
        placeholder="输入关键字搜索图数据库"
        @on-blur="searchDatabase"
        @mousedown.stop="handleMouseDown"
      />
    </div>
    <div class="database_info" v-else>
      <p class="name"><b>名称：</b>{{ properties.name }}</p>
      <p class="name"><b>昵称：</b>{{ properties.nickname }}</p>
      <p class="detail"><b>描述：</b>{{ properties.detail }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAnalysisStore } from '@/stores/analysis'
import { Input } from 'view-ui-plus'

const analysisStore = useAnalysisStore()
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
})
const properties = ref(props.properties)
const databaseInputValue = ref(analysisStore.databaseRelation.value)
const handleMouseDown = () => {
  event.stopPropagation()
}

const searchDatabase = () => {
  analysisStore.initSearchDatabase({ value: databaseInputValue.value.replace(/\s+/g, ' ').trim() })
}
</script>

<style lang="scss" scoped>
.database_node {
  width: 200px;
  height: 100px;
  border: 1px solid #acacac;
  background-color: #fff;
  display: flex;
  padding: 5px;
  img {
    width: 50px;
    height: 50px;
    margin-top: 10px;
    margin-right: 5px;
  }
  .database_info {
    width: 133px;
    color: #000;
    .name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .detail {
      height: 3em;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .database_input {
    width: 133px;
    margin-top: 10px;
    margin-bottom: 3px;
  }
}
:deep(.ivu-input) {
  resize: none;
}
</style>
