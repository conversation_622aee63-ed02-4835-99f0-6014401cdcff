import { fileURLToPath, URL } from 'node:url'
import fs from 'fs'
import { resolve } from 'path'
import https from 'https'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { viteStaticCopy } from 'vite-plugin-static-copy'

const agent = new https.Agent({
  pfx: fs.readFileSync(resolve('../../ssl/client.pfx')),
  servername: 'cd3.zhengzhoudacheng.com'
})

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    fs: {
      allow: ['node_modules'],
      strict: false
    },
    host: 'localhost',
    port: 8080,
    https: {
      ca: fs.readFileSync(resolve('../../ssl/ca.crt')),
      key: fs.readFileSync(resolve('../../ssl/server.key')),
      cert: fs.readFileSync(resolve('../../ssl/server.crt')),
      requestCert: true
    },
    proxy: {
      '/sw': {
        target: {
          protocol: 'https:',
          host: '**************',
          hostname: '**************',
          port: 8443
        },
        changeOrigin: false,
        secure: false,
        agent: agent,
        ws: true
      },
      '/plugin': {
        target: {
          protocol: 'https:',
          host: '**************',
          hostname: '**************',
          port: 8443
        },
        changeOrigin: false,
        secure: false,
        agent: agent,
        ws: true
      },
      '/data_analysis_platform/api/ws-jsonrpc/v1': {
        target: {
          protocol: 'https:',
          host: '**************',
          hostname: '**************',
          port: 8443
        },
        changeOrigin: false,
        secure: false,
        agent: agent,
        ws: true
      },
      '/case/api/ws-jsonrpc/v2': {
        target: {
          protocol: 'https:',
          host: '**************',
          hostname: '**************',
          port: 8443
        },
        changeOrigin: false,
        secure: false,
        agent: agent,
        ws: true
      },
      '/pki/api/ws-jsonrpc/v1': {
        target: {
          protocol: 'https:',
          host: '**************',
          hostname: '**************',
          port: 8443
        },
        changeOrigin: false,
        secure: false,
        agent: agent,
        ws: true
      },
      '/filesystem/api/ws-jsonrpc/v2': {
        target: {
          protocol: 'https:',
          host: '**************',
          hostname: '**************',
          port: 8443
        },
        changeOrigin: false,
        secure: false,
        agent: agent,
        ws: true
      },
      '/filesystem/api/rest/v2': {
        target: {
          protocol: 'https:',
          host: '**************',
          hostname: '**************',
          port: 8443
        },
        changeOrigin: false,
        agent: agent,
        secure: false,
        ws: true
      },
      '/eml_msg/api/ws-jsonrpc/v1': {
        target: {
          protocol: 'https:',
          host: '**************',
          hostname: '**************',
          port: 8443
        },
        changeOrigin: false,
        agent: agent,
        secure: false,
        ws: true
      },
      '/websocket/constant_data/api/ws-jsonrpc/v1': {
        target: {
          protocol: 'https:',
          host: '**************',
          hostname: '**************',
          port: 8443
        },
        changeOrigin: false,
        agent: agent,
        secure: false,
        ws: true
      }
    }
  },
  plugins: [
    vue(),
    viteStaticCopy({
      targets: [
        {
          src: 'public/*',
          dest: '.'
        },
        {
          src: '../../packages/certificate-login/src/service/wasm/dc_websocket_jsonrpc_client_golang.wasm',
          dest: 'pc/wasm'
        },
        {
          src: '../../packages/certificate-login/src/service/serviceWorker.js',
          dest: '.'
        }
      ]
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  build: {
    assetsDir: 'pc',
    rollupOptions: {
      output: {
        manualChunks: undefined,
        preserveModules: false
      }
    }
  }
})
