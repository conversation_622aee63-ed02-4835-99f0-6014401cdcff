<template>
  <div class="search_box">
    <Spin size="large" fix :show="searchStore.pathTreeLoad"></Spin>
    <!-- 搜索头部 -->
    <div class="search_header">
      <div class="data_num">
        <div v-if="searchStore.pathNumObj.countRes.num">
          <p>
            总数据量:
            <span>
              {{ $tools.formatNumber(searchStore.pathNumObj.countRes.num) }}
              <!-- 11.22亿 -->
            </span>
            条
          </p>
        </div>
        <div v-if="searchStore.pathNumObj.countRes.indexNum">
          <p>
            总索引量:
            <span>
              {{ $tools.formatNumber(searchStore.pathNumObj.countRes.indexNum) }}
              <!-- 11.22亿 -->
            </span>
            条
          </p>
          <Time
            v-if="searchStore.pathNumObj.countRes.timestamp"
            style="float: right"
            :time="searchStore.pathNumObj.countRes.timestamp"
          />
          <!-- <Time
            style="float: right"
            :time="1754035698"
          /> -->
        </div>
        <div 
          v-if="searchStore.pathNumObj.countSta.num"
          style="margin-left:10px"
        >
          <p>
            当前统计数据量:
            <span>
              {{$tools.formatNumber(searchStore.pathNumObj.countSta.num)}}
              <!-- 11.22亿 -->
            </span>
            条
          </p>
        </div>
        <div v-if="searchStore.pathNumObj.countSta.indexNum">
          <p>
            当前索引统计数据量:
            <span>
              {{$tools.formatNumber(searchStore.pathNumObj.countSta.indexNum)}}
              <!-- 11.22亿 -->
            </span>
            条
          </p>
          <Time
            v-if="searchStore.pathNumObj.countSta.timestamp"
            style="float: right"
            :time="searchStore.pathNumObj.countSta.timestamp"
          />
          <!-- <Time
            style="float: right"
            :time="1754035698"
          /> -->
        </div>
        
      </div>
      <Button
        type="success"
        size="small"
        title="统计所有路径下的数据量"
        @click="statisticPathNum"
        style="margin-top: 10px"
      >
        统计数据
      </Button>
    </div>
    <!-- 搜索内容 -->
    <div class="search_body">
      <div class="search">
        <div class="title">
          <Icon 
            type="ios-search" 
            size="30"
            color="#2D8CF0"
            style=""
          />
          <span>数据查询</span>
        </div>
        <div class="search_input">
          <div class="input_btn">
            <Input
              ref="searchInputRef"
              enter-button="搜&nbsp&nbsp&nbsp索"
              size="large"
              search
              clearable
              v-model="searchValue"
              placeholder="请输入查询关键字"
              @on-search="searchClick()"
              @on-focus="changeShowHisDom(true)"
              @on-blur="changeShowHisDom(false)"
            />
            <div
              class="search_his"
              v-if="hisValueData.length && showHisDom"
              @mouseenter="handleMouseEnter"
              @mouseleave="handleMouseLeave"
            >
              <div class="his_list">
                <List>
                  <ListItem
                    class="his_item"
                    v-for="item in hisValueData"
                    @click="searchHisValue(item)"
                    :key="item"
                  >
                    {{ item }}
                  </ListItem>
                </List>
              </div>
              <Button 
                style="float: right" 
                type="text" 
                icon="md-close" 
                @click="clearHisSearchArr"
              >
                清空历史搜索记录
              </Button>
            </div>
          </div>
        </div>
        <div class="search_type">
          <div class="tree">
            <Tree
              v-show="searchTreeVisable"
              multiple
              show-checkbox
              check-directly
              :data="searchStore.allPathTreeData"
              @on-check-change="dataTypeTreeChange"
            />
          </div>
          <div class="btn">
            <Button
              type="text"
              style="padding:0px"
              @click="showSearchTree"
            >
              选择数据类型
            </Button>
          </div>
        </div>
      </div>
      <div class="task">
        <Button 
          type="text"
          @click="showBatchQueryTaskDialog"
        >
          批量搜索
        </Button>
      </div>
    </div>
  </div>
  <!-- <Modal
      title="创建批量查询任务"
      width="800"
      v-model="batchQueryTaskDialogVisable"
      :closable="false"
      :mask-closable="false"
    >
      <div class="batchQuery">
        <Form :model="batchQueryItem" :label-width="100">
          <FormItem label="任务名：">
            <Input 
              v-model="batchQueryItem.taskName" 
              placeholder="请输入任务名"
            ></Input>
          </FormItem>
          <FormItem label="数据类型：">
            <TreeSelect
              v-model="batchQueryItem.dataType"
              placeholder="请选择数据类型"
              multiple
              show-checkbox
              :data="batchQueryItem.treeData"
              @on-change="selectChangePath"
              @on-open-change="unfoldPathTree"
            />
          </FormItem>
      </Form>
      </div>
    </Modal> -->
    <el-dialog
      v-model="batchQueryTaskDialogVisable"
      title="创建批量查询任务"
      width="800"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="batchQuery">
        <el-form :model="batchQueryItem" :label-width="100">
          <el-form-item label="任务名：">
            <el-input 
              v-model="batchQueryItem.taskName" 
              placeholder="请输入任务名"
            ></el-input>
          </el-form-item>
          <el-form-item label="数据类型：">
            <el-tree-select
              ref="dataTypeTreeRef"
              v-model="batchQueryItem.dataTypeTitle"
              placeholder="请选择数据类型"
              multiple
              clearable
              check-strictly
              default-expand-all
              show-checkbox
              collapse-tags
              collapse-tags-tooltip
              collapse-tags-tooltip-placement="right-start"
              :props="dataTypeProps"
              value-key="title"
              :data="batchQueryItem.treeData"
              node-key="title"
              popper-class="optionsContent"
              @check="dataTypeChange"
            />
          </el-form-item>
          <el-form-item label="查询字符串：">
            <el-tabs
              v-model="queryStringActiveTabName"
              type="card"
              :stretch="true"
              class="queryStringTabs"
              @tab-click="queryStringTabClick"
            >
              <el-tab-pane label="手动添加" name="manuallyAdd">
                <div class="manuallyAdd">
                  <el-tag
                    v-for="tag in batchQueryItem.manuallyQueryString"
                    size="large"
                    :key="tag"
                    closable
                    :disable-transitions="false"
                    @close="manuallyQueryTagClose(tag)"
                  >
                    {{ tag }}
                  </el-tag>
                  <el-input
                    v-if="manuallyInputVisible"
                    ref="manuallyInputRef"
                    v-model="manuallyInput"
                    class="manuallyInput"
                    size="default"
                    @keyup.enter="manuallyInputConfirm"
                    @blur="manuallyInputConfirm"
                  />
                  <el-button 
                    v-else 
                    class="button-new-tag" 
                    size="default" 
                    @click="showManuallyInput"
                  >
                    + 查询字符串
                  </el-button>
                </div>
              </el-tab-pane>
              <el-tab-pane label="批量添加" name="batchAdd">
                <el-input
                  v-model="batchQueryItem.batchQueryString"
                  :autosize="{ minRows: 8, maxRows: 11 }"
                  type="textarea"
                  placeholder="请输入查询字符串, 一行一个"
                />
              </el-tab-pane>
              <el-tab-pane label="文件导入" name="importFromFile">
                <el-upload
                  ref="fileUpload"
                  v-model:file-list="batchQueryItem.fileList"
                  :limit="1"
                  :on-exceed="fileExceed"
                  :on-change="fileProgress"
                  :auto-upload="false"
                >
                  <template #trigger>
                    <el-button type="primary">选择文件</el-button>
                  </template>
                  <template #tip>
                    <div>
                      只能上传一个小于1MB TXT文件; 一行一个查询字符串.
                    </div>
                  </template>
                </el-upload>
              </el-tab-pane>
            </el-tabs>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="closeBatchQueryTaskDialog()"
          >
            取 消
          </el-button>
          <el-button 
            type="primary" 
            @click="batchQueryTaskSummit"
          >
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>
</template>

<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount, inject } from 'vue'
import { useSearchStore } from '@/stores/search'
import { Message } from 'view-ui-plus'
import { ElMessage, genFileId } from 'element-plus'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'

const router = useRouter()
const $tools = inject('$tools')
const searchStore = useSearchStore()
const {
  searchValue,
  selectPathValue
} = storeToRefs(searchStore)

onMounted(() => {
  initialize()
  getPathNum()
  selectPathValue.value = searchStore.allPathArr
})

onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})

// 页面初始化
const initialize = () => {
  searchStore.searchValue = ''
  hisValueData.value = JSON.parse(localStorage.getItem('hisValueData')) || []
  searchStore.caseTreeLoad = true
  searchStore.pathTreeLoad = true
  // 获取路径树数据
  const selectPathData = localStorage.getItem('selectPathData')
  searchStore.clearPathTreeData()
  if (selectPathData) {
    searchStore.allPathTreeData = JSON.parse(selectPathData)
    searchStore.pathTreeLoad = false
  } else {
    searchStore.getPathTreeData()
  }
  const allPathArr = localStorage.getItem('allPathArr')
  const allPathDesc = localStorage.getItem('allPathDesc')
  if (allPathArr) {
    searchStore.allPathArr = JSON.parse(allPathArr)
  }
  if (allPathDesc) {
    searchStore.allPathDesc = JSON.parse(allPathDesc)
  }
  selectPathValue.value = allPathArr
}

// 请求统计数据
let timer = null
const getPathNum = () => {
  searchStore.getAllPathDataNum()
  timer = setInterval(() => {
    searchStore.getAllPathDataNum()
  }, 5000)
}

// 统计路径下的数据
const statisticPathNum = () => {
  searchStore.sendStatisticsPath()
}

// 搜索
const searchInputRef = ref()
const searchClick = (type) => {
  console.log("<searchClick> type:", type);
  console.log("<searchClick> selectPathValue:", selectPathValue.value);

  searchInputRef.value.blur()
  if (!selectPathValue.value.length){
    Message.error({
      background: true,
      content: '未选择路径'
    })
    return
  }
  
  setHisSearchValue()
  router.push('/search')
}

// 搜索历史记录
const hisValueData = ref(JSON.parse(localStorage.getItem('hisValueData')) || [])
const showHisDom = ref(false)
const isMouseOver = ref(false)
const isFoucs = ref(false)
const handleMouseEnter = () => {
  isMouseOver.value = true
}
const handleMouseLeave = () => {
  isMouseOver.value = false
  if (!isFoucs.value) {
    showHisDom.value = false
  }
}
const changeShowHisDom = (bool) => {
  console.log("<changeShowHisDom> bool:", bool);
  
  isFoucs.value = bool
  if (!isMouseOver.value) {
    showHisDom.value = bool
  }
}
const setHisSearchValue = () => {
  if (searchValue.value) {
    if (hisValueData.value.includes(searchValue.value)) {
      const index = hisValueData.value.indexOf(searchValue.value)
      hisValueData.value.splice(index, 1)
    }
    hisValueData.value.unshift(searchValue.value)
    if (hisValueData.value.length > 10) {
      hisValueData.value.pop()
    }
    localStorage.setItem('hisValueData', JSON.stringify(hisValueData.value))
  }
}
window.addEventListener('storage', (event) => {
  if (event.key === 'hisValueData') {
    hisValueData.value = JSON.parse(localStorage.getItem('hisValueData')) || []
  }
})
const searchHisValue = (item) => {
  searchValue.value = item
  // searchClick()
}
const clearHisSearchArr = () => {
  hisValueData.value = []
  localStorage.setItem('hisValueData', JSON.stringify(hisValueData.value))
}



// 数据类型选择
const searchTreeVisable = ref(false)
const showSearchTree = ()=>{
  console.log("<showSearchTree> searchTreeVisable:", searchTreeVisable);
  
  searchTreeVisable.value = !searchTreeVisable.value
  console.log("<showSearchTree> selectPathValue:", selectPathValue.value);
}

const dataTypeTreeChange = (list, node) => {
  console.log("<dataTypeTreeChange> list:", list);
  console.log("<dataTypeTreeChange> node:", node);
  
  selectPathValue.value = []
  list.forEach((item)=>{
    selectPathValue.value.push(item.value)
  })

  console.log("<dataTypeTreeChange> selectPathValue:", selectPathValue.value);
}

// 创建批量搜索任务
const batchQueryItem = ref({
  taskName: '',
  treeData: [],
  dataTypeTitle: [],
  dataTypePath: [],
  manuallyQueryString: [],
  batchQueryString: '',
  fileList: [],
  queryString: [],
  queryType: []
})
const dataTypeTreeRef = ref()
const dataTypeProps = {
  children: 'children',
  label: 'title',
} 
const queryStringActiveTabName = ref('manuallyAdd')
const batchQueryTaskDialogVisable = ref(false)

const showBatchQueryTaskDialog = ()=>{
  batchQueryItem.value.treeData = JSON.parse(localStorage.getItem('selectPathData'))
  batchQueryItem.value.dataTypeTitle = JSON.parse(localStorage.getItem('allDataTypeTitle'))

  console.log("<showBatchQueryTaskDialog> batchQueryItem:", batchQueryItem);
  searchTreeVisable.value = false
  batchQueryTaskDialogVisable.value = true
}

// 选择路径变化
const dataTypeChange = (data, checkedStatus) => {
  console.log('<dataTypeChange> data:', data);
  console.log('<dataTypeChange> checkedStatus:', checkedStatus);

  let node = dataTypeTreeRef.value.getNode(data)
  console.log('<dataTypeChange> node:', node);
  let isChecked = node.checked
  console.log('<dataTypeChange> isChecked:', isChecked);

  if (data.children && isChecked) {
    console.log('<dataTypeChange> checked');
    selectChildren(data, true);
  } else if (data.children && !isChecked) {
    console.log('<dataTypeChange> cancel');
    selectChildren(data, false);
  }
}

const selectChildren = (node, checked) => {
  // 递归选择或取消选择子节点
  node.children.forEach((child) => {
    if (child.children){
      selectChildren(child, checked)
    }
    if (checked) {
      batchQueryItem.value.dataTypeTitle.push(child.title);
      batchQueryItem.value.dataTypeTitle = Array.from(
        new Set(batchQueryItem.value.dataTypeTitle)
      )
    } else {
      const index = batchQueryItem.value.dataTypeTitle.indexOf(child.title);
      if (index > -1) {
        batchQueryItem.value.dataTypeTitle.splice(index, 1);
      }
    }
  });
}

const queryStringTabClick = (tab, event) => {
  console.log("<queryStringTabClick>:",tab, event)
}

const createBatchQueryTask = ()=> {
  let tmpType = [...new Set(batchQueryItem.value.dataTypeTitle)]
  tmpType.splice(tmpType.indexOf('/'), 1)
  
  searchStore.createBatchQueryTask({
    taskName: batchQueryItem.value.taskName,
    queryString: [...new Set(batchQueryItem.value.queryString)],
    dataType:  [...new Set(batchQueryItem.value.dataTypePath)],
    dataTypeTitle: tmpType
  })
}
        
const batchQueryTaskSummit = ()=>{
  console.log("<batchQueryTaskSummit> batchQueryItem:", batchQueryItem.value);

  if (!batchQueryItem.value.taskName){
    ElMessage({
      message: '请输入任务名!',
      grouping: true,
      type: 'error',
    })
    return
  }

  let checkedNodes = dataTypeTreeRef.value.getCheckedNodes()
  console.log("<batchQueryTaskSummit> checkedNodes:", checkedNodes);
  
  checkedNodes.forEach((item)=>{
    if (item.value != '/'){
      batchQueryItem.value.dataTypePath.push(item.value)
    }
  })

  if (!batchQueryItem.value.dataTypePath.length){
    ElMessage({
      message: '请选择数据类型!',
      grouping: true,
      type: 'error',
    })
    return
  }

  switch (queryStringActiveTabName.value){
    case 'manuallyAdd':
      if (!batchQueryItem.value.manuallyQueryString.length){
        ElMessage({
          message: '请输入查询字符串!',
          grouping: true,
          type: 'error',
        })
        return
      }

      batchQueryItem.value.queryString = batchQueryItem.value.manuallyQueryString
      createBatchQueryTask()
      closeBatchQueryTaskDialog()
      break;
    case 'batchAdd':
      if (!batchQueryItem.value.batchQueryString){
        ElMessage({
          message: '请输入查询字符串!',
          grouping: true,
          type: 'error',
        })
        return
      }

      let tmpStr = batchQueryItem.value.batchQueryString.split('\n')
      batchQueryItem.value.queryString = tmpStr.filter(item => item !== undefined && item !== null && item !== '')
      // searchStore.createBatchQueryTask({
      //   taskName: batchQueryItem.value.taskName,
      //   queryString: [...new Set(batchQueryItem.value.queryString)],
      //   dataType:  tmpType,
      // })
      createBatchQueryTask()
      closeBatchQueryTaskDialog()
      break;
    case 'importFromFile':
      if (!batchQueryItem.value.fileList.length){
        ElMessage({
          message: '请输入查询字符串!',
          grouping: true,
          type: 'error',
        })
        return
      }
      readFileContent()
      break;
    default:
      return;
  }
}
  
const closeBatchQueryTaskDialog = () => {
  batchQueryTaskDialogVisable.value = false
  queryStringActiveTabName.value = 'manuallyAdd'
  batchQueryItem.value = {
    taskName: '',
    treeData: [],
    dataTypeTitle: [],
    dataTypePath: [],
    manuallyQueryString: [],
    batchQueryString: '',
    fileList: [],
    queryString: [],
    queryType: []
  }
}

// 手动添加
const manuallyInputRef = ref(null)
const manuallyInput = ref('')
const manuallyInputVisible = ref(false)

const manuallyQueryTagClose = (tag) => {
  batchQueryItem.value.manuallyQueryString.splice(batchQueryItem.value.manuallyQueryString.indexOf(tag), 1)
}

const manuallyInputConfirm = () => {
   if (manuallyInput.value) {
    batchQueryItem.value.manuallyQueryString.push(manuallyInput.value)
  }
  manuallyInputVisible.value = false
  manuallyInput.value = ''
}

const showManuallyInput = ()=>{
  manuallyInputVisible.value = true
  nextTick(() => {
    manuallyInputRef.value.input.focus()
  })
}

// 文件导入
const fileUpload = ref()
const fileExceed = (files) => {
  console.log("<fileExceed> files:", files);

  fileUpload.value.clearFiles()
  const file = files[0]
  file.uid = genFileId()
  fileUpload.value.handleStart(file)
}

const fileProgress = (file, fileList) => {
  console.log("<fileProgress> file:", file);
  console.log("<fileProgress> fileList:", fileList);

  if (file.raw.type != "text/plain"){
    ElMessage({
      message: '不支持的文件类型!',
      grouping: true,
      type: 'error',
    })
    fileUpload.value.clearFiles()
    return
  }

  if (file.size > 1 * 1024 * 1024){
    console.log("<fileProgress> 文件过大");
    ElMessage({
      message: '文件过大!',
      grouping: true,
      type: 'error',
    })
    fileUpload.value.clearFiles()
    return
  }
}

const readFileContent = () => {
  if (!batchQueryItem.value.fileList) return
  
  const reader = new FileReader()
  let fileContent = []
  reader.onload = (e) => {
    fileContent = e.target.result.split('\r\n')
    console.log("<readFileContent> fileContent:", fileContent);
    batchQueryItem.value.queryString = fileContent.filter(item => item !== undefined && item !== null && item !== '')
    // searchStore.createBatchQueryTask({
    //   taskName: batchQueryItem.value.taskName,
    //   queryString: [...new Set(batchQueryItem.value.queryString)],
    //   dataType:  tmpType,
    // })
    createBatchQueryTask()
    closeBatchQueryTaskDialog()
  }
  
  reader.onerror = () => {
    ElMessage.error('文件读取失败')
  }
  
  reader.readAsText(batchQueryItem.value.fileList[0].raw)
}



</script>

<style scoped lang="scss">
.search_box{
  height: 98.5%;
  width: 99.4%;
  border-radius: 5px;
  margin-top: 6px;
  margin-left: 6px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  .search_header {
    height: 160px;
    width: 100%;
    margin-top: 10px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    padding-right: 10px;
    .data_num {
      display: flex;
      p {
        font-weight: bold;
        margin-left: 10px;
        font-size: 12px;
        span {
          color: #2b85e4;
          padding: 0 5px;
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }
  .search_body {
    width: 45%;
    margin-left: 28%;
    margin-top: 6%;
    display: flex;
    flex-direction: row;
    .search{
      width: 90%;
      display: flex;
      flex-direction: column;
      align-items: center;
      .title{
        span{
          font-size: 24px;
        }
      }
      .search_input {
        width: 100%;
        margin-top: 20px;
        // display: flex;
        // justify-content: center;
        .input_btn {
          width: 100%;
          position: relative;
        }
        .search_his {
          position: absolute;
          width: 87.3%;
          background: #fff;
          border: 1px solid #e2e2e2;
          border-radius: 2px;
          z-index: 5;
          top: 40px;
          left: 0;
          .his_list {
            max-height: 260px;
            overflow: auto;
            .his_item {
              padding-left: 20px;
              cursor: pointer;
            }
            .his_item:hover {
              background-color: #eeecec;
            }
          }
        }
      }

      .search_type {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        .tree{
          width: 100%;
          height: 400px;
          overflow-y: auto;
        }
        .btn{
          .ivu-btn:focus {
            // outline: none;
            box-shadow: none;
          }
        }
      }
    }
    .task{
      margin-top: 58px;
      .ivu-btn:focus {
        // outline: none;
        box-shadow: none;
      }
    }
  }
  
}
.batchQuery{
  height: 400px;
  width: 750px;
  .queryStringTabs{
    width: 100%;
  }
  .manuallyAdd{
    max-height: 240px;
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
    gap: .5rem;
    .manuallyInput{
      width: 80px;
    }
  }
}
:deep(.ivu-select-dropdown) {
  max-height: 300px;
}
:deep(.ivu-select-default.ivu-select-multiple .ivu-select-selection) {
  max-height: 32px;
  overflow: hidden;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

</style>
<style lang="scss">
.optionsContent {
  max-width: 40%;
  max-height: 40%;
  overflow: auto;
  padding: 10px;
  gap: 10rem !important;
}
</style>
